import { createAction, createReducer } from '@reduxjs/toolkit';
import getApiAuthToken from '../../../helpers/getApiAuthToken';
import { get, isUndefined } from 'lodash';
import fetch from '../../../helpers/fetchRetry';
// import flareData from './d3-flare-example.json';
export const FETCH_SUNBURST_AGGREGATIONS = createAction(
  'fetch sunburst aggregations start'
);
export const FETCH_SUNBURST_AGGREGATIONS_SUCCESS = createAction<{
  aggregations: Sunburst;
}>('fetch sunburst aggregations success');
// don't need to pass payload
export const FETCH_SUNBURST_AGGREGATIONS_FAILURE = createAction(
  'fetch sunburst aggregations failure'
);
export const UPDATE_SELECTED_ENTITY = createAction<{
  selectedEntity: string[];
  queryType: string;
}>('selected entity updated ');
export const SEARCH_PATH_ENTITY_TYPE = 'entity-extraction.entity.class';
export const SEARCH_PATH_ENTITY_ENTITY = 'entity-extraction.entity.label';

export const SUNBURST_AGGREGATION_QUERY_CLAUSE = {
  name: 'type',
  field: SEARCH_PATH_ENTITY_TYPE,
  operator: 'term',
  limit: 100000,
  aggregate: [
    {
      name: 'entity',
      field: SEARCH_PATH_ENTITY_ENTITY,
      operator: 'term',
      limit: 100000,
      aggregate: [
        {
          name: 'tdoCount',
          field: 'recordingId',
          operator: 'count',
          limit: 100000,
        },
      ],
    },
  ],
};

export const ENTITY_COLORS = [
  '#6CC7E9',
  '#DEF59D',
  '#FF8B95',
  '#E6A9D0',
  '#fdc28c',
  '#A9E5DE',
  '#E87ADE',
  '#B5DABE',
  '#F74E4E',
  '#5ED2FF',
  '#FFA28C',
  '#FFC4CA',
  '#C44B87',
  '#00FCDF',
  '#7E7B9F',
  '#00BBB2',
  '#FFCD49',
  '#FFB299',
  '#ADF805',
  '#3D84D4',
  '#51F3A8',
  '#07BCFF',
  '#E7E4FF',
  '#FF3EA2',
  '#98FFDA',
] as const;

export const ENTITY_CHILDREN_COLORS = [
  '#4DB3DA',
  '#CDE290',
  '#F05E6A',
  '#BE81C0',
  '#fda762',
  '#81D7CD',
  '#CF50C4',
  '#72D78B',
  '#E60000',
  '#2DC5FF',
  '#FF8669',
  '#FFA7B0',
  '#B01F66',
  '#00E8CD',
  '#5D5889',
  '#019A92',
  '#FFBF15',
  '#FF936F',
  '#9CE100',
  '#0153AF',
  '#02E27A',
  '#00A8E6',
  '#CECAEE',
  '#ED007B',
  '#7CF8CC',
] as const;

// These we do not want to see on the chart
const IGNORED_ENTITY_TYPES: any = new Set([]);

// This is an arbitrary number, out of the blue, means nothing. These should be driven by the UI
const MIN_ENTITY_RESULT_ITEMS = 31;

const defaultState = {
  schema: null,
  fetchingAggregations: false,
  analyticsData: {} as any, // TODO: better type
  selectedPathReadableValue: null as string | null,
  selectedSchemaPropertyPath: null as string | null,
  selectedSchemaPropertyValue: null as string | null,
  selectedEntity: [] as string[],
};

interface Bucket {
  doc_count: number;
  entity: {
    doc_count_error_upper_bound: number;
    sum_other_doc_count: number;
    buckets: {
      doc_count: number;
      key: string;
      tdoCount: { doc_count: number; tdoCount: { value: number } };
    }[];
  };
  key: string;
}

interface Sunburst {
  type: {
    type: {
      doc_count: number;
      type: {
        sum_other_doc_count: number;
        doc_count_error_upper_bound: number;
        buckets: {
          doc_count: number;
          key: string;
          entity: {
            sum_other_doc_count: number;
            doc_count_error_upper_bound: number;
            buckets: {
              key: string;
              doc_count: number;
              tdoCount: {
                doc_count: number;
                tdoCount: {
                  value: number;
                };
              };
            }[];
          };
        }[];
      };
    };
  };
}
const reducer = createReducer(defaultState, (builder) => {
  builder
    .addCase(FETCH_SUNBURST_AGGREGATIONS, (state) => {
      return {
        ...state,
        fetchingAggregations: true,
        selectedPathReadableValue: null,
        selectedSchemaPropertyPath: null,
        selectedSchemaPropertyValue: null,
      };
    })
    .addCase(FETCH_SUNBURST_AGGREGATIONS_SUCCESS, (state, action) => {
      // const responseAggregations = get(action, 'payload.aggregations', {});
      const responseAggregations = get(
        action,
        'payload.aggregations',
        undefined
      );
      // TODO: do smth with this messy triple deep response - a different search query ?
      // const entityTypeBuckets = get(
      //   responseAggregations,
      //   'type.type.type.buckets',
      //   []
      // );
      const entityTypeBuckets =
        responseAggregations?.type?.type?.type?.buckets || [];

      const analyticsData = bucketsToAnalyticsChartData(entityTypeBuckets);
      return {
        ...state,
        analyticsData,
        fetchingAggregations: false,
      };
    })
    .addCase(FETCH_SUNBURST_AGGREGATIONS_FAILURE, (state) => {
      return {
        ...state,
        fetchingAggregations: false,
      };
    })
    .addCase(UPDATE_SELECTED_ENTITY, (state, action) => {
      const selectedEntity = get(action, 'payload.selectedEntity');
      return {
        ...state,
        selectedEntity,
      };
    });
});

function uniqueId() {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
}

export const dedupBuckets = (buckets: Bucket[]) => {
  const category: { [key: string]: { [key: string]: number } } = {};
  for (const buc of buckets) {
    const key = buc.key.toUpperCase();
    if (!buc.entity || !buc.entity.buckets || !buc.entity.buckets.length) {
      continue;
    }
    let subCategory = category[key];
    if (!subCategory) {
      subCategory = {};
      category[key] = subCategory;
    }
    for (const subBuc of buc.entity.buckets) {
      if (!subBuc.key) {
        continue;
      }
      const count = Number(get(subBuc, 'tdoCount.tdoCount.value'));
      if (!count) {
        continue;
      }
      const oldCount = subCategory[subBuc.key];
      if (oldCount) {
        subCategory[subBuc.key] = +count + oldCount;
      } else {
        subCategory[subBuc.key] = +count;
      }
    }
  }
  return category;
};

interface AnalyticsChartDataChild extends AnalyticsChartData {
  value: number;
}

interface AnalyticsChartData {
  name: string;
  hex: string;
  schemaPropertyPath: string;
  id: string;
  children?: AnalyticsChartDataChild[];
}

const bucketsToAnalyticsChartData = (typeBuckets: Bucket[]) => {
  if (!typeBuckets || !typeBuckets.length) {
    return {};
  }
  const buckets = typeBuckets.filter(
    (typeBucket) => !IGNORED_ENTITY_TYPES.has(typeBucket.key)
  );
  if (!buckets.length) {
    return {};
  }
  const analyticsChartData: AnalyticsChartData[] = [];
  const dataBuckets = dedupBuckets(buckets);
  const categories = Object.entries(dataBuckets);
  categories.forEach(([categoryKey, subCategories], i) => {
    const hexBucketColor = getBucketColorHex(i, ENTITY_COLORS);
    const hexChildBucketColor = getBucketColorHex(i, ENTITY_CHILDREN_COLORS);
    const rootItemData: AnalyticsChartData = {
      name: categoryKey,
      hex: hexBucketColor,
      schemaPropertyPath: 'type',
      id: uniqueId(),
    };
    rootItemData.children = [];
    for (const subKey of Object.keys(subCategories)) {
      const count = subCategories[subKey];
      if (isUndefined(count)) {
        continue;
      }
      rootItemData.children.push({
        name: subKey,
        value: count,
        hex: hexChildBucketColor,
        schemaPropertyPath: 'entity',
        id: uniqueId(),
      });
    }
    if (rootItemData.children.length > MIN_ENTITY_RESULT_ITEMS) {
      rootItemData.children = rootItemData.children
        .sort((a: { value: number }, b: { value: number }) => b.value - a.value)
        .slice(0, MIN_ENTITY_RESULT_ITEMS - 1);
    }
    analyticsChartData.push(rootItemData);
  });
  return {
    children: analyticsChartData,
  };
};

export const getBucketColorHex = (
  bucketIndex: number,
  colors: readonly [string, ...string[]]
) => {
  const color = colors[bucketIndex % colors.length] ?? colors[0];
  return color.substring(1);
};

export default reducer;
export const namespace = 'sunburst';
export const local = (state: any) => state[namespace] as typeof defaultState;

export const fetchAggregations =
  (aggregateQuery: any) => async (dispatch: any, getState: any) => {
    const state = getState();
    const endpoint = `${state.config.apiRoot}/api/search/aggregate`;
    const token = getApiAuthToken(state);
    const veritoneAppId = state.config.veritoneAppId;
    const headers: { [key: string]: string } = {
      Authorization: 'Bearer ' + token,
      'Content-type': 'application/json',
    };
    if (veritoneAppId) {
      headers['x-veritone-application'] = veritoneAppId;
    }

    const enableTimeWarnings = endpoint.includes('stage');
    const reqStartTime = Date.now();

    let result;
    try {
      result = await fetch(endpoint, {
        method: 'POST',
        headers,
        body: JSON.stringify(aggregateQuery),
      })
        .then((res) => res.text())
        .then((resText) => JSON.parse(resText))
        .catch((error) => console.log(error));
    } catch (_err) {
      dispatch(FETCH_SUNBURST_AGGREGATIONS_FAILURE());
      return;
    }

    const reqEndTime = Date.now();
    if (enableTimeWarnings && reqEndTime - reqStartTime > 5000) {
      console.error(
        `Request finished in ${(reqEndTime - reqStartTime) / 1000}s`,
        { endpoint, query: JSON.stringify(aggregateQuery) }
      );
    }

    if (!result || result.error) {
      dispatch(FETCH_SUNBURST_AGGREGATIONS_FAILURE());
      return;
    }
    dispatch(FETCH_SUNBURST_AGGREGATIONS_SUCCESS(result));
  };

export const emitFetchAggregationsAction = () => (dispatch: any) => {
  dispatch(FETCH_SUNBURST_AGGREGATIONS());
};

export const isFetching = (state: any) => local(state).fetchingAggregations;
export const getAnalyticsData = (state: any) => local(state).analyticsData;
export const getSelectedEntity = (state: any) => local(state).selectedEntity;
