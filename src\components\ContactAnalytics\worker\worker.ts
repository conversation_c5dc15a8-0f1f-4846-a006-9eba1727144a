import { createStore, applyMiddleware, Action } from 'redux';
import createSagaMiddleware from 'redux-saga';
import { thunk } from 'redux-thunk';
import reducer from './reducer';
import sagas from './sagas';
import analyticsConfigUpdateMiddleware from './analyticsConfigUpdateMiddleware';
import * as actions from './actions';

const sagaMiddleware = createSagaMiddleware();

const middlewares = [thunk, sagaMiddleware, analyticsConfigUpdateMiddleware];

if (process.env.NODE_ENV === 'development') {
  // TODO: Fix
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  const { createLogger } = require(`redux-logger`);

  middlewares.push(
    createLogger({
      collapsed: true,
    })
  );
}

const store = createStore(reducer as any, applyMiddleware(...middlewares));

sagaMiddleware.run(sagas);

onmessage = function (e) {
  try {
    const { actionName, args } = e.data;
    // Assume this will  work, and handle failure with the catch
    const handler = (
      actions as unknown as Record<string, (...args: any[]) => Action>
    )[actionName]!;
    store.dispatch(handler(...JSON.parse(args)));
  } catch (err) {
    console.error('worker failed to dispatch action', err, e);
  }
};

store.subscribe(() => {
  postMessage({
    state: store.getState(),
  });
});
