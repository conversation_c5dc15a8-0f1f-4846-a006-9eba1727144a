Feature: Search

  # @e2e @search
  # Scenario: Search by Keyword
  #   When The user searches by keyword "Welcome" to find file "e2e_audio.mp3"

  # @e2e @search
  # Scenario: Search by tag
  #   When The user searches by tag "tagSearch" to find file "bloomberg.mp4"

  # @e2e @search
  # Scenario: Search by bookmark
  #   When The user searches by bookmark "bookmarkSear" to find file "e2e_video.mp4"

  # @e2e @search
  # Scenario: Verify UI of search bar
  #   When The user clicks on the search bar
  #   Then The search bar pop-up is displayed
  #   And The pop-up contains a list of searchable options
  #   And The default focus is on "Search by Keyword"
  #   And The text "Search by keyword within our database of transcripts." is displayed
  #   And A text box to input phrase to search exists with placeholder "Phrase to search"
  #   And The "Close" and "Add" buttons are displayed
  #   When The user clicks the "Close" button on the search bar pop-up
  #   Then The search bar pop-up is no longer visible

  @e2e @search_keyword
  Scenario: Verify user search for a keyword
    When The user adds the following keywords to the search:
      | keyword  |
      | Facebook |
    Then The file list is displayed
    When The user clicks on the file "e2e_audio.mp3"
    Then The media details page is displayed
    And The transcript contains the following highlighted keywords:
      | keyword  |
      | Facebook |

  @e2e @search_multiple_keywords
  Scenario: Verify user can search for multiple keywords in the same tdo at the same time
    When The user adds the following keywords to the search:
      | keyword  |
      | Facebook |
      | Apple    |
    Then The file list is displayed
    When The user clicks on the file "e2e_audio.mp3"
    Then The media details page is displayed
    And The transcript contains the following highlighted keywords:
      | keyword  |
      | Facebook |
      | Apple    |
    And The transcript focuses on the first highlighted keyword "Facebook"
