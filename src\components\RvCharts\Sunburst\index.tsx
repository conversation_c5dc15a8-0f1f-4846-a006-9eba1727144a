import { useState, useEffect } from 'react';
import { ConnectedProps, connect } from 'react-redux';
import { Sunburst, LabelSeries } from 'react-vis';
import { cloneDeep, isEmpty } from 'lodash';
import { EXTENDED_DISCRETE_COLOR_RANGE } from '../theme';
import {
  isFetching,
  getAnalyticsData,
  getSelectedEntity,
  UPDATE_SELECTED_ENTITY,
  FETCH_SUNBURST_AGGREGATIONS,
} from 'state/modules/sunburst';
import { QUERY_TYPE } from 'state/modules/search';
import CircularProgress from '@mui/material/CircularProgress';
import * as Styles from './styles.scss';
import { setSelection } from '../../../state/modules/tdosTable';
import { FETCH_MEDIA_AGGREGATIONS } from 'state/modules/dashboard';
const LABEL_STYLE = {
  fontSize: '12px',
  fontFamily: 'Roboto',
  textAnchor: 'middle',
  fontcolor: 'rgba(0,0,0,0.54)',
};

function BasicSunburst(props: Props) {
  const [mouseoverEntityPath, setMouseoverEntityPath] = useState<string[]>([]);
  const [isSelected, setIsSelected] = useState(!isEmpty(props.selectedEntity));

  useEffect(() => {
    setMouseoverEntityPath(props.selectedEntity);
    setIsSelected(!isEmpty(props.selectedEntity));
  }, [props.selectedEntity]);

  function cleanUpStates() {
    setMouseoverEntityPath([]);
    setIsSelected(false);
  }

  function onValueMouseOver(node: any) {
    if (isSelected) {
      return;
    }
    const path = getKeyPath(node, 'name').reverse();
    const entityPath = path?.filter((name) => name !== 'root') ?? [];
    setMouseoverEntityPath(entityPath);
  }

  function onValueMouseOut() {
    if (isSelected) {
      return;
    }
    setMouseoverEntityPath([]);
  }

  function onValueClick() {
    const newValueIsSelected = !isSelected;
    setIsSelected(newValueIsSelected);
    if (newValueIsSelected) {
      props.updateSelectedEntity(mouseoverEntityPath, QUERY_TYPE.ANALYTICS);
    } else {
      // no selection was made so emit action without payload values
      props.updateSelectedEntity([], QUERY_TYPE.ANALYTICS);
      cleanUpStates();
      props.fetchSunburstAggregations();
    }
    props.fetchMediaAggregations();
    props.updateSelection([], false);
  }

  const { analyticsData, isFetchingAnalyticsData, sizeSunburst, windowSize } =
    props;
  const sunburstData = cloneDeep(analyticsData);
  if (!isEmpty(mouseoverEntityPath)) {
    const pathAsMap = {} as { [key: string]: boolean };
    let tmpDate = sunburstData;
    for (const name of mouseoverEntityPath) {
      tmpDate = tmpDate?.children?.find(
        (data: { name: string; children: any[] }) => data.name === name
      );
      if (!tmpDate) {
        break;
      }
      pathAsMap[tmpDate.id] = true;
    }
    updateData(sunburstData, pathAsMap);
  }
  const chartLabel =
    mouseoverEntityPath?.[1] ?? mouseoverEntityPath?.[0] ?? 'Explorer';
  // change Sunburst size according to container size
  let viewSize = 0.8;
  if (sizeSunburst.w < 2 && sizeSunburst.h > 4) {
    viewSize = 1.8;
    if (windowSize < 1280) {
      viewSize = 1.2;
    }
  } else {
    if (sizeSunburst.h > 4) {
      viewSize = 1.8;
    }
  }

  return (
    <div className={Styles.container}>
      <div className={Styles.title}>Entity Clusters</div>
      <div className={Styles.viz}>
        {isFetchingAnalyticsData && (
          <CircularProgress size={125} thickness={1} />
        )}
        {!isFetchingAnalyticsData && (
          <Sunburst
            animation
            hideRootNode
            onValueMouseOver={onValueMouseOver}
            onValueMouseOut={onValueMouseOut}
            onValueClick={onValueClick}
            style={{
              stroke: '#ddd',
              strokeOpacity: 0.3,
              strokeWidth: '0.5',
            }}
            colorType="literal"
            getSize={(d: any) => d.value}
            getColor={(d: any) => `#${d.hex}`}
            data={sunburstData}
            height={350 * viewSize}
            width={350 * viewSize}
          >
            <LabelSeries
              data={[{ x: 0, y: 0, label: chartLabel, style: LABEL_STYLE }]}
            />
          </Sunburst>
        )}
      </div>
    </div>
  );
}

type Props = PropsFromRedux & {
  sizeSunburst: {
    h: number;
    w: number;
  };
  windowSize: number;
};

/**
 * Recursively work backwards from highlighted node to find path of value   nodes
 * @param {Object} node - the current node being considered
 * @param {String} valueType - a string instructs function to get either id or name
 * @returns {Array} an array of strings describing the key route to the current node
 */
function getKeyPath(node: any, valueType: any): Array<any> {
  if (!node.parent) {
    return ['root'];
  }

  return [node.data?.[valueType] || node[valueType]].concat(
    getKeyPath(node.parent, valueType)
  );
}

/**
 * Recursively modify data depending on whether or not each cell has been selected by the hover/highlight
 * @param {Object} data - the current node being considered
 * @param {Object|Boolean} keyPath - a map of keys that are in the highlight path
 * if this is false then all nodes are marked as selected
 * @returns {Object} Updated tree structure
 */
function updateData(data: any, keyPath?: any) {
  if (!data) {
    return {};
  }
  if (data.children) {
    data.children.forEach((child: any) => updateData(child, keyPath));
  }
  // add a fill to all the uncolored cells
  if (!data.hex) {
    data.style = {
      fill: EXTENDED_DISCRETE_COLOR_RANGE[5],
    };
  }
  data.style = {
    ...data.style,
    fillOpacity: keyPath && !keyPath[data.id] ? 0.2 : 1,
  };
  return data;
}

const mapState = (state: any) => ({
  isFetchingAnalyticsData: isFetching(state),
  analyticsData: getAnalyticsData(state),
  selectedEntity: getSelectedEntity(state),
});

const mapDispatch = {
  updateSelectedEntity: (selectedEntity: string[], queryType: string) =>
    UPDATE_SELECTED_ENTITY({ selectedEntity, queryType }),
  fetchMediaAggregations: () => FETCH_MEDIA_AGGREGATIONS(),
  fetchSunburstAggregations: () => FETCH_SUNBURST_AGGREGATIONS(),
  updateSelection: (
    selectedRows: (string | number)[],
    isSelectedAll: boolean
  ) => setSelection(selectedRows, isSelectedAll),
};

const connector = connect(mapState, mapDispatch);

type PropsFromRedux = ConnectedProps<typeof connector>;

export default connector(BasicSunburst);
