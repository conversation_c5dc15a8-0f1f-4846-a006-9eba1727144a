import { SearchBar } from './index';
import { render, screen } from '@testing-library/react';
jest.mock('@veritone/glc-advanced-search-bar');

describe('SearchBar', () => {
  it('renders a div element with data-testid = entity-search', () => {
    // @ts-expect-error TODO: Fix
    render(<SearchBar apiRoot="https://test.com" />);
    expect(screen.getByTestId('entity-search')).toBeInTheDocument();
  });
});
