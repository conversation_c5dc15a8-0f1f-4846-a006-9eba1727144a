import {
  Before,
  Then,
  When,
  DataTable,
} from '@badeball/cypress-cucumber-preprocessor';
import { mediaListPage } from '../../../pages/mediaListPage';

Before(() => {
  cy.LoginLandingPage();
  mediaListPage.goToMediaListPage();
});

When(
  'The user searches by keyword {string} to find file {string}',
  (keyword: string, fileName: string) => {
    mediaListPage.searchByKeyword(keyword, fileName);
  }
);

When(
  'The user searches by tag {string} to find file {string}',
  (tag: string, fileName: string) => {
    mediaListPage.searchByTag(tag, fileName);
  }
);

When(
  'The user searches by bookmark {string} to find file {string}',
  (bookmark: string, fileName: string) => {
    mediaListPage.searchByBookmark(bookmark, fileName);
  }
);

When('The user clicks on the search bar', () => {
  cy.get('[data-test="appbarSearch"]').click();
});

Then('The search bar pop-up is displayed', () => {
  cy.get('[role="document"]').should('be.visible');
});

Then('The pop-up contains a list of searchable options', () => {
  cy.get('[data-test="engineCategoryCard"]').should('exist');
});

Then('The default focus is on {string}', (text: string) => {
  cy.get('[data-veritone-element="search-category-label-transcript"]').should(
    'have.text',
    text
  );
  cy.get(`[title="${text}"]`)
    .should('have.attr', 'style')
    .and('include', 'background-color');
});

Then('The text {string} is displayed', (text: string) => {
  cy.contains(text).should('be.visible');
});

Then(
  'A text box to input phrase to search exists with placeholder {string}',
  (placeholder: string) => {
    cy.get('#transcript_search_field')
      .should('exist')
      .should('have.attr', 'placeholder', placeholder);
  }
);

Then(
  'The {string} and {string} buttons are displayed',
  (closeButtonText: string, addButtonText: string) => {
    cy.get('[data-test="searchModalActionBtnClose"]')
      .should('exist')
      .should('have.text', closeButtonText);
    cy.get('[data-test="searchModalActionBtn"]')
      .should('exist')
      .should('have.text', addButtonText);
  }
);

When(
  'The user clicks the {string} button on the search bar pop-up',
  (buttonText: string) => {
    if (buttonText === 'Close') {
      cy.get('[data-test="searchModalActionBtnClose"]').click();
    } else if (buttonText === 'Add') {
      cy.get('[data-test="searchModalActionBtn"]').click();
    }
  }
);

Then('The search bar pop-up is no longer visible', () => {
  cy.get('[role="document"]').should('not.exist');
});

Then('The file list is displayed', () => {
  cy.get('[data-testid="file-paging"]').should('be.visible');
});

When('The user clicks on the file {string}', (fileName: string) => {
  cy.get(`[data-testid^="files-table-row"]`).contains(fileName).click();
});

Then('The media details page is displayed', () => {
  cy.get('#media-details-page-content').should('be.visible');
});

When(
  'The user adds the following keywords to the search:',
  (dataTable: DataTable) => {
    cy.get('[data-test="appbarSearch"]').click();

    dataTable.rows().forEach((row: string[]) => {
      const keyword = row[0];
      if (keyword) {
        cy.get('#transcript_search_field').type(keyword);
        cy.get('[data-test="searchModalActionBtn"]').contains('Add').click();
        cy.get('#transcript_search_field').should('have.value', '');
      }
    });

    cy.get('[data-test="searchModalActionBtnClose"]').click();
    cy.get('[role="document"]').should('not.exist');
  }
);

Then(
  'The transcript contains the following highlighted keywords:',
  (dataTable: DataTable) => {
    dataTable.rows().forEach((row: string[]) => {
      const keyword = row[0];
      if (keyword) {
        cy.get(`span[class*="_highlightSearch"]`)
          .contains(keyword)
          .should('be.visible')
          .then(($el: JQuery<HTMLElement>) => {
            const element = $el[0]!;
            const computedStyle = window.getComputedStyle(element);
            expect(computedStyle.backgroundColor).to.equal(
              'rgb(255, 225, 104)'
            );
            return null;
          });
      }
    });
  }
);

Then(
  'The transcript focuses on the first highlighted keyword {string}',
  (keyword: string) => {
    cy.get(`span[class*="_highlightSearch"]`)
      .first()
      .should('contain.text', keyword)
      .then(($el: JQuery<HTMLElement>) => {
        const element = $el[0]!;
        const computedStyle = window.getComputedStyle(element);
        expect(computedStyle.backgroundColor).to.equal('rgb(255, 225, 104)');
        return null;
      });
  }
);
