/* cspell:words pendo */

import { get } from 'lodash';
import { modules } from '@veritone/glc-redux';

const {
  config: { getConfig },
  user,
} = modules;

function initSegment(state: any, page: any) {
  const analytics: any = (window.analytics = window.analytics || []);
  if (!analytics.initialize && !analytics.invoked) {
    analytics.invoked = !0;
    analytics.methods = [
      'trackSubmit',
      'trackClick',
      'trackLink',
      'trackForm',
      'pageview',
      'identify',
      'reset',
      'group',
      'track',
      'ready',
      'alias',
      'debug',
      'page',
      'once',
      'off',
      'on',
    ];
    analytics.factory = function (t: typeof analytics.methods) {
      return function (...args: unknown[]) {
        const e = [t, ...args];
        analytics.push(e);
        return analytics;
      };
    };

    for (const method of analytics.methods) {
      analytics[method] = analytics.factory(method);
    }
    analytics.load = function (t: any, e: any) {
      const n = document.createElement('script');
      n.type = 'text/javascript';
      n.async = !0;
      n.src =
        'https://cdn.segment.com/analytics.js/v1/' + t + '/analytics.min.js';
      const a: any = document.getElementsByTagName('script')[0];
      a && a.parentNode.insertBefore(n, a);
      analytics._loadOptions = e;
    };
    analytics.SNIPPET_VERSION = '4.1.0';
    analytics.load(getConfig(state).segmentWriteKey); // todo grab from configs
    analytics.page(page);

    const currentUser = user.selectUser(state);
    analytics.identify(`${currentUser.userId}`, {
      email: currentUser.userName,
      'VTN-Illuminate': 'True',
    });
  }

  return analytics;
}

function initPendo(state: any) {
  const pendo: any = (window.pendo = window.pendo || []);
  if (pendo.initialize) {
    return;
  }

  const currentUser = user.selectUser(state);
  const userGroup = currentUser?.groups?.[0];
  const organization = currentUser?.organization;
  const pendoKey = getConfig(state).pendoKey;
  if (!pendoKey) {
    return;
  }
  const visitor = {
    id: currentUser.userId,
    email: currentUser.userName,
  };
  const account = {
    id: userGroup?.groupId,
    groupname: userGroup?.groupName,
    organizationId: organization?.organizationId,
    organizationName: organization?.organizationName,
    planLevel: get(organization, 'kvp.accountProfile'),
  };
  // pendo init magic
  (function (apiKey) {
    (function (p: any, e, n, d: any) {
      let w, x;
      const o: any = (p[d] = p[d] || {});
      o._q = [];
      const v = ['initialize', 'identify', 'updateOptions', 'pageLoad'];
      for (w = 0, x = v.length; w < x; ++w) {
        (function (m) {
          o[m] =
            o[m] ||
            function (...args: any[]) {
              o._q[m === v[0] ? 'unshift' : 'push']([m, ...args]);
            };
        })(v[w]!); // Safe due to for loop checks
      }

      const y: any = e.createElement(n);
      y.async = !0;
      y.src = 'https://cdn.pendo.io/agent/static/' + apiKey + '/pendo.js';
      const z: any = e.getElementsByTagName(n)[0];
      z && z.parentNode.insertBefore(y, z);
    })(window, document, 'script', 'pendo');
    window.pendo.initialize({
      visitor: visitor,
      account: account,
    });
  })(pendoKey);
}

export const getSegment = initSegment;

export const getPendo = (state: any) => {
  initPendo(state);
  return window.pendo;
};
